<div class="flex flex-col">
  <div class="flex justify-between items-center">
    <h3 class="text-lg font-medium">Média</h3>

    <% if block.media_group&.media_type %>
      <%= link_to new_admin_content_medium_path(block_id: block.id, media_type_id: block.media_group.media_type.id), class: "btn btn-sm btn-primary", data: { turbo_frame: "media_sidebar" } do %>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
          <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
        </svg>
        <span>Přidat <%= block.media_group.media_type.name %></span>
      <% end %>
    <% elsif block.media_type.present? %>
      <% if block.media_type %>
        <%= link_to new_admin_content_medium_path(block_id: block.id, media_type_id: block.media_type.id), class: "btn btn-sm btn-primary", data: { turbo_frame: "media_sidebar" } do %>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
            <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
          </svg>
          <span>Přidat <%= block.media_type.name %></span>
        <% end %>
      <% else %>
        <div class="text-gray-500">Typ média '<%= block.media_type.name %>' není definován</div>
      <% end %>
    <% end %>
  </div>

  <% if block.media_type.has_groups? %>
    <%= form_with model: [@page, @block], url: admin_page_block_path(@block, page_id: @block.page), method: :patch, id: "media-block-form", data: { controller: "form" }, class: "flex flex-col h-full" do |form| %>

      <div>
        <label class="text-xs">Zdroj obsahu</label>
        <%= form.combobox :media_group_id, MediaGroup.with_media,
                          data: { action: "hw-combobox:selection->form#requestSubmit" }, render_in: { partial: "admin/content/blocks/media_controls/hw_combobox_search_image" } %>
      </div>
      <% end %>
  <% end %>

  <% if block.media.any? %>
    <%= render "admin/content/media/media_items", block: block %>
  <% else %>
    <div class="mt-4 p-4 bg-gray-50 rounded border border-gray-200 text-center text-gray-500">
      Zatím nejsou přidána žádná média. Klikněte na tlačítko "Přidat" pro přidání nového média.
    </div>
  <% end %>
</div>