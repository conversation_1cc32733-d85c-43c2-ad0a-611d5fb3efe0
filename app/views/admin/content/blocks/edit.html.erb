<%= turbo_frame_tag :sidebar do %>
  <style>
      .blocks-container { pointer-events: none;  }
      .block-overlay-lg { display: block !important; }
      .block-overlay { display: block !important; }
      .editable-header { display: none }
      #edit-block-container-<%= @block.id %> .block-overlay { display: none !important; }
      #edit-block-container-<%= @block.id %> { pointer-events: all !important; }
      #edit-block-container-<%= @block.id %> .block-overlay-lg { display: none !important; }
      .block-toolbar { display: none !important; }
      .block-editable-item { display: block !important; }
  </style>

  <script>
      // Tento skript sa pokúsi nájsť element na hlavnej stránke (mimo sidebaru)
      // a zoskrolovať naň. Uistite sa, že element s týmto ID existuje.
      const el = document.getElementById('block-container-<%= @block.id %>');
      if (el) {
          el.scrollIntoView({
              behavior: 'auto',
              block: 'center',
              inline: 'center'
          });
      }
  </script>

  <div class="w-80">
  <div class="fixed top-0 left-0 w-80 h-screen bg-gray-50 z-[1000] flex flex-col">
    <%= turbo_frame_tag :media_sidebar %>

    <%# Skrolovateľná obsahová časť sidebaru %>
    <div
      class="flex-1 overflow-y-auto"
      data-controller="options"
      data-options-id-value="<%= @block.id.to_i %>"
    >
      <div class="w-full flex justify-between pt-4 px-3">
        <div>
          <a href="<%= admin_block_path(@page) %>" data-turbo-frame="_top" data-turbo-method="get" data-turbo-confirm="Zavřít bez uložení?" class="p-1 rounded-md hover:bg-gray-200 text-gray-800 flex space-x-2">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
            </svg>
          </a>
        </div>
      </div>

      <%# Kontajner pre záložky a ich obsah %>
      <div class="relative px-3 pb-4">
        <div data-controller="tabs" data-tabs-active-tab-class="-mb-px border-b-2 border-gray-800 text-black">
          <%# Navigácia záložiek - sticky, aby zostala viditeľná pri skrolovaní obsahu panelov %>
          <ul class="list-reset flex border-b border-gray-200 text-gray-800 sticky top-0 bg-gray-50 z-10">
            <% if @block.has_media? %>
              <li class="mr-1 hover:bg-gray-100" data-tabs-target="tab" data-action="click->tabs#change">
                <button type="button" class="cursor-pointer inline-block py-2 px-4 text-sm no-underline">Media</button>
              </li>
            <% end %>
            <% if @block.has_pricing? %>
              <li class="mr-1 hover:bg-gray-100" data-tabs-target="tab" data-action="click->tabs#change">
                <button type="button" class="cursor-pointer inline-block py-2 px-4 text-sm no-underline">Ceník</button>
              </li>
            <% end %>
            <% if @block.controls.any? %>
              <li class="-mb-px mr-1 hover:bg-gray-100" data-tabs-target="tab" data-action="click->tabs#change">
                <button type="button" class="cursor-pointer inline-block py-2 px-4 text-sm no-underline">Obsah</button>
              </li>
            <% end %>
            <li class="mr-1 hover:bg-gray-100" data-tabs-target="tab" data-action="click->tabs#change">
              <button type="button" class="cursor-pointer inline-block py-2 px-4 text-sm no-underline">Vzhled</button>
            </li>
            <% if @block.name == "spacer" %>
              <li class="mr-1 hover:bg-gray-100" data-tabs-target="tab" data-action="click->tabs#change">
                <button type="button" class="inline-block py-2 px-4 text-sm no-underline">Nastavení</button>
              </li>
            <% end %>
          </ul>
          
          <% if @block.has_media? %>
            <div class="hidden py-4" data-tabs-target="panel">
              <div id="media-content">
                <%= render "/admin/content/media/media", block: @block %>
              </div>
            </div>
          <% end %>

          <%= form_with model: [@page, @block.becomes(Block)], url: admin_page_block_path(@page, @block), data: { turbo_frame: :_top }, method: :patch, id: "block-form", class: "flex flex-col" do |form| %>

            <%# Panel pre Cenník %>
            <% if @block.has_pricing? %>
              <div class="hidden py-4" data-tabs-target="panel">
                <%= render "/admin/content/blocks/pricing_options", form: form %>
              </div>
            <% end %>

            <%# Panel pre Obsah (dynamické polia) %>
            <% if @block.controls.any? %>
              <div class="hidden pt-4" data-tabs-target="panel">
                <div class="flex flex-col space-y-4">
                  <% @block.controls.each do |control| %>
                    <%= form.fields_for :controls, control do |b| %>
                      <%= b.hidden_field :type %>
                      <%= render b.object.edit_template_file, form: form, b: b, control: control, block: @block unless control.edit_template_file.nil? %>
                    <% end %>
                  <% end %>
                </div>
              </div>
            <% end %>

            <%# Panel pre Vzhľad %>
            <div class="hidden py-4" data-tabs-target="panel">
              <%= render 'block_theme_form', form: form, block: @block %>
            </div>

            <%# Panel pre Nastavení (špecifické pre blok "spacer") %>
            <% if @block.name == "spacer" %>
              <div class="hidden py-4" data-tabs-target="panel">
                <%# Nastavenie veľkosti %>
                <div class="w-full flex justify-between items-center space-x-3">
                  <label class="text-sm w-1/2">Velikost</label>
                  <div class="flex-1" data-controller="slider">
                    <div data-slider-target="handler" class="slider-styled slider-round"></div>
                    <%= form.hidden_field :height, min: 1, max: 10, data: { 'slider-target': "input" },  class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
                  </div>
                </div>

                <%# Nastavenie farby pozadia %>
                <div class="flex justify-between items-center space-x-3 py-2 mt-4">
                  <label class="text-sm w-1/2">Barva pozadí</label>
                  <div data-controller="color-picker">
                    <%= form.hidden_field :background_color, data: { 'color-picker-target': :input } %>
                    <div data-color-picker-target="button"></div>
                  </div>
                </div>
              </div>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>

    <%# Pätička sidebaru s tlačidlom Uložiť %>
    <div id="submit-block-form" class="w-full bg-white border-t border-gray-200 flex-shrink-0">
      <div class="px-3 py-4 bg-gray-50"> <%# Zjednotená farba pozadia s hornou časťou sidebaru %>
        <button type="submit" class="bg-black p-2 text-center text-white rounded w-full cursor-pointer hover:bg-gray-800" form="block-form">Uložit blok</button>
      </div>
    </div>
  </div>
  </div>
<% end %>