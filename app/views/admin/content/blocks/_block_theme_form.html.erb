<%= form.fields_for :options, block do |classes| %>
  <div data-controller="block" class="h-screen flex flex-col overflow-y-scroll" id="block-design-form" data-block-container-value="<%= block.container_class %>" data-block-alignment-value="<%= block.alignment %>" data-block-background-color-value="<%= block.background_color %>" data-block-inner-container-value="<%= block.inner_container %>" data-block-content-container-value="<%= block.content_container %>" data-block-id-value="<%= block.id %>" data-block-theme-value="<%= block.theme %>">
    <div class="flex flex-col gap-y-2">

      <fieldset class="fieldset  bg-base-100 border border-base-300 px-4 pb-3 rounded-box">
        <legend class="fieldset-legend"><span class="w-3 h-3 bg-green-500 rounded-full"></span> Obsah</legend>

        <div class="flex flex-col space-y-2.5">
          <div class="flex justify-between items-center relative">
            <label class="fieldset-label text-gray-700 w-1/2">Téma</label>
            <div class="flex relative"
                 data-controller="dropdown"
                 data-action="click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide"
            >
              <button type="button" data-action="dropdown#toggle:stop"  data-dropdown-target="button" class="flex items-center space-x-0.5 bg-white text-black text-sm py-1 px-1.5 rounded-md border border-gray-200 cursor-pointer hover:bg-gray-50">
                <span class="flex">
                  <% @colors.each do |key, options| %>
                    <div data-theme="<%= key %>" class="hidden selected-<%= key %> <%= options[:background] %> bg-base-100 w-4 h-4 border border-gray-400 rounded-full"></div>
                  <% end %>
                </span>

                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                  <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                </svg>
              </button>
              <div data-dropdown-target="menu" class="hidden absolute top-4 right-0 z-50 mt-4 shadow-xl flex w-screen max-w-max p-1.5 bg-white rounded-md border border-gray-300">
                <div class="text-sm overflow-hidden w-52 grid grid-cols-6 gap-x-1.5">
                  <% @colors.each do |key, options| %>
                    <label data-theme="<%= key %>" data-controller="tippy" data-tippy-content="<%= key %>" data-tippy-placement="bottom" class="no-underline bg-white cursor-pointer py-1 px-1.5 flex items-center justify-center rounded text-gray-900 hover:bg-gray-100">

                      <div class="flex">
                        <div class="<%= key %> bg-base-100 w-5 h-5 border border-gray-300 rounded-full"></div>
                      </div>
                      <span class="hidden"><%= options[:name] %></span>

                      <%= classes.radio_button :theme, key, { 'data-action': 'dropdown#change dropdown#toggle block#onChangeTheme', 'data-dropdown-target': 'input', class: "hidden" } %>
                    </label>
                  <% end %>
                </div>
              </div>
            </div>
          </div>

          <div class="w-full flex justify-between items-center space-x-3">
            <label class="fieldset-label text-gray-700 w-1/2">Odsazení obsahu</label>
            <div class="flex-1" data-controller="slider" data-slider-element-value="block-controls-<%= block.id %>" data-slider-current-value="<%= block.outer_padding_class %>" data-slider-values-value='["gap-y-0", "gap-y-0.5", "gap-y-1", "gap-y-1.5", "gap-y-2", "gap-y-2.5", "gap-y-3", "gap-y-3.5", "gap-y-4", "gap-y-4.5", "gap-y-5", "gap-y-5.5", "gap-y-6", "gap-y-8", "gap-y-10", "gap-y-12", "gap-y-16", "gap-y-20", "gap-y-24"]'>
              <div data-slider-target="handler" class="slider-styled slider-round"></div>
              <%= classes.hidden_field :gap_y, value: block.gap_y_class, min: 0, max: 16, data: { 'slider-target': "input" },  class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
            </div>
          </div>

          <div class="w-full flex justify-between items-center space-x-3">
            <label class="fieldset-label text-gray-700 w-1/2">Odsazení Y</label>
            <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>-content-container" data-slider-current-value="<%= block.padding_class %>" data-slider-values-value='["sm:py-0", "sm:py-0.5", "sm:py-1", "sm:py-2", "sm:py-4", "sm:py-6", "sm:py-8", "sm:py-10", "sm:py-12", "sm:py-16", "sm:py-20", "sm:py-24", "sm:py-32", "sm:py-36", "sm:py-48", "sm:py-56", "sm:py-64"]'>
              <div data-slider-target="handler" class="slider-styled slider-round"></div>
              <%= classes.hidden_field :padding, value: block.padding_class, min: 0, max: 16, data: { 'slider-target': "input" },  class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
            </div>
          </div>

          <div class="w-full flex justify-between items-center space-x-3">
            <label class="fieldset-label text-gray-700 w-1/2">Odsazení X</label>
            <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>-content-container" data-slider-current-value="<%= block.padding_x_class %>" data-slider-values-value='["sm:px-0", "sm:px-0.5", "sm:px-1", "sm:px-2", "sm:px-4", "sm:px-6", "sm:px-8", "sm:px-10", "sm:px-12", "sm:px-16", "sm:px-20", "sm:px-24", "sm:px-32", "sm:px-36", "sm:px-48", "sm:px-56", "sm:px-64"]'>
              <div data-slider-target="handler" class="slider-styled slider-round"></div>
              <%= classes.hidden_field :padding_x, value: block.padding_x_class, min: 0, max: 16, data: { 'slider-target': "input" },  class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
            </div>
          </div>

          <div class="w-full flex justify-between items-center space-x-3">
            <label class="fieldset-label text-gray-700 w-1/2">Velikost</label>
            <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>-content-container" data-slider-current-value="<%= block.container_class %>" data-slider-values-value='["max-w-xl", "max-w-2xl", "max-w-3xl", "max-w-4xl", "max-w-5xl", "max-w-6xl", "w-full"]'>
              <div data-slider-target="handler" class="slider-styled slider-round"></div>
              <%= block.container_class %>
              <%= classes.hidden_field :content_container, value: block.container_class, min: 0, max: 6, data: { 'slider-target': "input" }, class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
            </div>
          </div>

          <div class="w-full flex justify-between items-center space-x-3">
          <label class="fieldset-label text-gray-700 w-1/2">Zarovnání</label>
          <fieldset>
            <div class="grid grid-cols-3 gap-3 sm:grid-cols-3">
              <% %w[left center right].each do |align| %>
                <%= classes.radio_button :alignment, align, class: "peer/#{align} hidden", 'data-action': 'block#onChangeAlignment', 'data-dropdown-target': 'input', id: "alignment-#{align}" %>
                <label for="alignment-<%= align %>" class="flex cursor-pointer items-center justify-center border border-gray-100 rounded-md p-1.5 text-sm font-semibold uppercase focus:outline-hidden sm:flex-1 bg-white peer-checked/<%= align %>:bg-black peer-checked/<%= align %>:text-white">
                <span>
                  <% if align == "left" %>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                      <path fill-rule="evenodd" d="M2 3.75A.75.75 0 0 1 2.75 3h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 8Zm0 4.25a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
                    </svg>
                  <% elsif align == "center" %>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                      <path fill-rule="evenodd" d="M2 3.75A.75.75 0 0 1 2.75 3h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 8Zm0 4.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
                    </svg>
                  <% elsif align == "right" %>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                      <path fill-rule="evenodd" d="M2 3.75A.75.75 0 0 1 2.75 3h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 8Zm6 4.25a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75Z" clip-rule="evenodd" />
                    </svg>
                  <% end %>
                </span>
                </label>
              <% end %>
            </div>
          </fieldset>
        </div>
        </div>
      </fieldset>

      <fieldset class="fieldset  bg-base-100 border border-base-300 px-4 pb-3 rounded-box">
        <legend class="fieldset-legend"><span class="w-3 h-3 bg-orange-500 rounded-full"></span> Vnitřní kontejner</legend>
        <div class="flex flex-col space-y-2">

        </div>

        <div class="w-full flex justify-between items-center space-x-3">
          <label class="fieldset-label text-gray-700 w-1/2">Velikost</label>
          <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>-inner-container" data-slider-current-value="<%= block.inner_container %>" data-slider-values-value='["sm:w-xl", "sm:w-2xl", "sm:w-3xl", "sm:w-4xl", "sm:w-5xl", "sm:w-6xl", "w-full"]'>
            <div data-slider-target="handler" class="slider-styled slider-round"></div>
            <%= classes.hidden_field :inner_container, value: block.inner_container, min: 0, max: 6, data: { 'slider-target': "input" }, class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
          </div>
        </div>
      </fieldset>

        <fieldset class="fieldset  bg-base-100 border border-base-300 px-4 pb-3 rounded-box">
          <legend class="fieldset-legend"><span class="w-3 h-3 bg-purple-500 rounded-full"></span> Vnější kontejner</legend>

          <div class="flex flex-col space-y-2">
            <div class="flex justify-between items-center">
              <label class="fieldset-label text-gray-700 w-1/2">Téma</label>
              <div class="flex relative"
                   data-controller="dropdown"
                   data-action="click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide"
              >
                <button type="button" data-action="dropdown#toggle:stop" data-dropdown-target="button" class="flex items-center space-x-0.5 bg-white text-black text-sm py-1 px-1.5 rounded-md border border-gray-200">
                    <span class="flex">
                      <% @colors.each do |key, options| %>
                        <div data-theme="<%= key %>" class="hidden selected-<%= key %> <%= options[:background] %> bg-base-100 w-4 h-4 border border-gray-400 rounded-full"></div>
                      <% end %>
                    </span>

                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                    <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                  </svg>
                </button>
                <div data-dropdown-target="menu" class="hidden absolute top-4 right-0 z-50 mt-5 flex w-screen max-w-max p-3 bg-white rounded-md border border-gray-400">
                  <div class="text-sm w-44">
                    <label data-theme="auto" class="justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100">
                      <div class="flex space-x-1.5">
                        <div class="flex">
                          <div class="auto bg-base-100 w-5 h-5 border border-gray-400 rounded-full"></div>
                        </div>
                        <span>Auto</span>
                      </div>
                      <%= classes.radio_button :background_color, :auto, { 'data-action': 'dropdown#change dropdown#toggle block#onChangeBackgroundColor', 'data-dropdown-target': 'input' } %>
                    </label>

                    <% @colors.each do |key, options| %>
                      <label data-theme="<%= key %>" class="justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100">
                        <div class="flex space-x-1.5">
                          <div class="flex">
                            <div class="<%= key %> bg-base-100 w-5 h-5 border border-gray-400 rounded-full"></div>
                          </div>
                          <span><%= options[:name] %></span>
                        </div>
                        <%= classes.radio_button :background_color, key, { 'data-action': 'dropdown#change dropdown#toggle block#onChangeBackgroundColor', 'data-dropdown-target': 'input' } %>
                      </label>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>

            <div class="w-full flex justify-between items-center space-x-3">
              <label class="fieldset-label text-gray-700 w-1/2">Překrytí</label>
              <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>-overlay" data-slider-current-value="opacity-<%= block.background_overlay_opacity || 0 %>" data-slider-values-value='["opacity-0", "opacity-10", "opacity-20", "opacity-30", "opacity-40", "opacity-50", "opacity-60", "opacity-70", "opacity-80", "opacity-90", "opacity-100"]'>
                <div data-slider-target="handler" class="slider-styled slider-round"></div>
                <%= classes.hidden_field :background_overlay_opacity, min: 0, max: 10, data: { 'slider-target': "input" }, class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
              </div>
            </div>


            <div class="w-full flex justify-between items-center space-x-3">
              <label class="fieldset-label text-gray-700 w-1/2">Velikost</label>
              <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>" data-slider-current-value="<%= block.container_class %>" data-slider-values-value='["max-w-xl", "max-w-2xl", "max-w-3xl", "max-w-4xl", "max-w-5xl", "max-w-6xl", "w-full"]'>
                <div data-slider-target="handler" class="slider-styled slider-round"></div>
                <%= classes.hidden_field :container, value: block.container_class, min: 0, max: 6, data: { 'slider-target': "input" }, class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
              </div>
            </div>

            <div class="w-full flex justify-between items-center space-x-3">
              <label class="fieldset-label text-gray-700 w-1/2">Odsazení - Y</label>
              <div class="flex-1" data-controller="slider" data-slider-element-value="block-<%= block.id %>" data-slider-current-value="<%= block.outer_padding_class %>" data-slider-values-value='["sm:py-0", "sm:py-0.5", "sm:py-1", "sm:py-2", "sm:py-4", "sm:py-6", "sm:py-8", "sm:py-10", "sm:py-12", "sm:py-16", "sm:py-20", "sm:py-24", "sm:py-32", "sm:py-64"]'>
                <div data-slider-target="handler" class="slider-styled slider-round"></div>
                <%= classes.hidden_field :outer_padding, value: block.outer_padding_class, min: 0, max: 12, data: { 'slider-target': "input" },  class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
              </div>
            </div>
          </div>

          <hr class="border-gray-300 my-3">

          <label class="fieldset-label text-black">Obrázek</label>
          <%= form.file_field :background_image, class: "file-input file-input-xs" %>

          <% if block.background_image.present? && block.background_image.attached? %>
            <div class="flex items-center space-x-3">
              <%= image_tag block.background_image.variant(:thumb) %>

              <a href="" class="btn btn-sm btn-outline">
                Smazat
              </a>
            </div>
          <% end %>
        </fieldset>
    </div>
  </div>
<% end %>