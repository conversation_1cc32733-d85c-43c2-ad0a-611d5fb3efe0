<%= form_with model: [:admin, current_tenant], url: admin_settings_path do |f| %>
  <div class="relative bg-white flex">
    <div class="flex-1 focus:outline-none">
      <div class="relative max-w-4xl mx-auto md:px-8 xl:px-0">
        <div class="py-10">
          <div class="px-4 sm:px-6 md:px-0 flex space-x-2 items-center">
            <h1 class="text-2xl font-semibold text-gray-900">
              Základní nastavení
            </h1>
          </div>
          <div class="mt-5">
            <div class="bg-white flex flex-col space-y-4">
              <div>
                <label>Název webu</label>
                <div>
                  <%= f.text_field :name, class: "input" %>
                </div>
              </div>

              <div>
                <label>Logo webu</label>
                <div class="flex items-center space-x-4">
                  <% if current_tenant.logo.attached? %>
                    <div class="flex-shrink-0">
                      <%= display_logo(current_tenant.logo) %>
                    </div>
                  <% end %>
                  <div class="flex-1">
                    <%= f.file_field :logo, accept: "image/svg+xml,image/png,image/jpg,image/jpeg,image/gif", class: "file-input" %>
                    <p class="text-xs text-gray-500 mt-1">Podporované formáty: SVG, PNG, JPG, GIF</p>
                  </div>
                </div>
              </div>

              <div>
                <label>Kontaktní telefon</label>
                <div>
                  <%= f.text_field :phone, class: "input" %>
                  <% if current_tenant.errors[:phone].any? %>
                  <p class="form-input-error">Zadejte platné telefonní číslo</p>
                  <% end %>
                </div>
              </div>

              <div>
                <label>E-mail</label>
                <div>
                  <%= f.text_field :email, class: "input" %>
                  <% if current_tenant.errors[:email].any? %>
                    <p class="form-input-error">Zadejte platný e-mail</p>
                  <% end %>
                </div>
              </div>

              <div class="field">
                <label>Výchozí jazyk webu</label>
                <%= f.select :locale, locale_options_for_select, {}, { class: "select" } %>
              </div>

              <div class="field">
                <label>Další jazyky</label>
                <div class="flex space-x-2">
                  <% locale_options_for_select.each do |name, locale| %>
                    <% next if locale.to_s == current_tenant.locale.to_s  %>

                    <label class="language-checkbox">
                      <%= f.check_box :available_locales, { multiple: true, class: 'hidden', checked: current_tenant.available_locales.include?(locale.to_s) }, locale, nil %>
                      <span class="language-button"><%= name %></span>
                    </label>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="py-3 bg-avocado-100/70 border-t-2 border-avocado-200">
    <div class="max-w-4xl mx-auto">
      <button type="submit" class="btn btn-primary">Uložit nastavení</button>
    </div>
  </div>
<% end %>
