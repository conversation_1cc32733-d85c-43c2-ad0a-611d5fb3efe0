module BlockViews
  class MediaViews::Gallery001Component < BaseComponent
    def view_template
      container do
        inner_container do
          content_container do
            div(id:"block-controls-#{block_object.id}", class: "#{block_object.gap_y_class} mx-auto flex text-center justify-center flex-col mb-6 sm:mb-0") do
              block_object.controls.each do |control|
                render control.component
              end
            end

            media_container do
              render Ui::Gallery.new(
                block_object.media.items,
                layout: block_object.media.options[:layout] || :grid,
                columns: 4,
                gap: block_object.media.options[:gap] || 3,
                resize_options: resize_image_options,
                item_classes: "relative aspect-square overflow-hidden rounded-md"
              )
            end
          end
        end
      end
    end

    private

    def resize_image_options
      { resize_to_fill: [200, 200] }
    end
  end
end
