module BlockViews
  class HeroViews::Hero006Component < BaseComponent
    def view_template
      container do
        inner_container do
          content_container(css: "mx-auto gap-16 md:grid md:grid-cols-2") do
           div(id:"block-controls-#{@block_object.id}", class: "#{@block_object.gap_y_class} mx-auto flex flex-col mb-6 md:mb-0") do
            @block_object.controls.each do |control|
              render control.component
            end
          end

           media_container(css: "flex self-center items-center") do
             Ui::Gallery(
               block_object.media.items,
               layout: block_object.media.options[:layout] || :grid,
               columns: block_object.media.options[:inline_items_count] || 3,
               gap: block_object.media.options[:gap] || 3,
               resize_options: resize_image_options,
             )
           end
         end
        end
      end
    end

    private

    def resize_image_options
      { resize_to_fill: [300, 400] }
    end
  end
end
