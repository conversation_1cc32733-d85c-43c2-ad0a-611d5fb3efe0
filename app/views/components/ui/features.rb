class Ui::Features < ApplicationComponent
  include I<PERSON><PERSON><PERSON><PERSON><PERSON>

  def initialize(items, layout: :default, columns: 3, gap: 4)
    @items = items
    @layout = layout
    @columns = columns
    @gap = gap
  end

  def view_template
    case @layout.to_sym
    when :default
      render_default
    when :cards
      render_cards
    when :icons
      render_icons
    else
      render_default
    end
  end

  private

  def render_default
    div(class: "grid grid-cols-1 sm:grid-cols-2 space-y-4 sm:space-y-0 md:grid-cols-#{@columns} gap-#{@gap}") do
      @items.each do |item|
        div(class: "feature relative sm:p-3 flex flex-col space-y-3") do
          div(class: "flex space-x-3 items-center") do
            span(class: "icon rounded p-2 bg-accent text-accent-content") { render_icon(item) }
            h4 { item_title(item) }
          end
          div(class: "text-sm", id: "media-#{item.id}-text") { item.text.html_safe }
        end
      end
    end
  end

  def render_cards
    div(class: "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-#{@columns} gap-#{@gap}") do
      @items.each do |item|
        div(class: "feature relative p-6 rounded-lg shadow-sm flex flex-col space-y-4") do
          div(class: "flex items-center space-x-3") do
            span(class: "icon rounded-full p-3 bg-primary-100") { render_icon(item) }
            h4(class: "font-medium") { item_title(item) }
          end
          p(class: "text-sm text-gray-600") { item_body(item) }
        end
      end
    end
  end

  def render_icons
    div(class: "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-#{@columns} gap-#{@gap}") do
      @items.each do |item|
        div(class: "feature relative text-center flex flex-col items-center space-y-4") do
          span(class: "icon rounded-full p-4 bg-primary-100") { render_icon(item, size: 6) }
          h4(class: "font-medium") { item_title(item) }
          p(class: "text-sm text-gray-600") { item_body(item) }
        end
      end
    end
  end

  def render_icon(item, size: 5)
    icon_name = if item.respond_to?(:icon) && item.icon.present?
                  item.icon
                elsif item.is_a?(Hash) && item[:icon].present?
                  item[:icon]
                elsif item.is_a?(Hash) && item[:icon_name].present?
                  item[:icon_name]
                else
                  "check"
                end

    Icon.by_name(icon_name, size: size)
  end

  def item_title(item)
    if item.respond_to?(:title) && item.title.present?
      item.title
    elsif item.is_a?(Hash) && item[:title].present?
      item[:title]
    else
      "Feature"
    end
  end
end
