class Ui::Image < ApplicationComponent
  include Phlex::Rails::Helpers::ImageTag
  include Phlex::Rails::Helpers::AssetPath

  def initialize(image, resize_options: {}, classes: "", lazy: true, alt: "")
    @image = image
    @resize_options = resize_options
    @classes = classes
    @lazy = lazy
    @alt = alt
  end

  def view_template
    if @image.nil?
      render_placeholder
    elsif @image.is_a?(String)
      render_url_image
    elsif @image.is_a?(ActiveStorage::Attached::One) && @image.attached?
      render_active_storage_image
    elsif @image.is_a?(ActiveStorage::Attachment)
      render_active_storage_attachment
    elsif @image.respond_to?(:image) && @image.image.attached?
      render_model_with_image
    else
      render_placeholder
    end
  end

  private

  def render_url_image
    img(src: @image, class: @classes, loading: @lazy ? "lazy" : "eager", alt: @alt)
  end

  def render_active_storage_image
    variant = @resize_options.present? ? @image.variant(@resize_options) : @image
    #img(src: Rails.application.routes.url_helpers.rails_blob_path(variant, only_path: true), class: @classes, loading: @lazy ? "lazy" : "eager", alt: @alt || @image.filename.to_s)
  end

  def render_active_storage_attachment
    variant = @resize_options.present? ? @image.variant(@resize_options) : @image
    img(src: Rails.application.routes.url_helpers.rails_blob_path(variant, only_path: true), class: @classes, loading: @lazy ? "lazy" : "eager", alt: @alt || @image.filename.to_s)
  end

  def render_model_with_image
    variant = @resize_options.present? ? @image.image.variant(@resize_options) : @image.image
    img(src: url_for(variant), class: @classes, loading: @lazy ? "lazy" : "eager", alt: @alt || @image.image.filename.to_s)
  end

  def render_placeholder
    div(class: "w-full h-full bg-gray-100 flex items-center justify-center #{@classes}") do
      svg(xmlns: "http://www.w3.org/2000/svg", class: "h-12 w-12 text-gray-400", fill: "none", viewbox: "0 0 24 24", stroke: "currentColor") do
        path(stroke_linecap: "round", stroke_linejoin: "round", stroke_width: "2", d: "M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z")
      end
    end
  end
end
