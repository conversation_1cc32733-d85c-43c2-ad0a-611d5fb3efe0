class BlockObject
  attr_reader :id, :name, :options, :media, :controls, :context, :pricing_id, :pricing_options, :background_image, :background_image_mobile

  def initialize(id:, name:, context:, options:, controls:, media:, pricing_id:, pricing_options:, background_image: nil, background_image_mobile: nil)
    @id = id
    @name = name
    @context = context
    @options = options.symbolize_keys
    @controls = controls
    @media = media
    @pricing_id = pricing_id
    @pricing_options = pricing_options
    @background_image = background_image
    @background_image_mobile = background_image_mobile
    @component = nil
  end

  def context=(context)
    @context ||= context
  end

  def container_class
    @options[:container]
  end

  def padding_class
    @options[:padding]
  end

  def padding_x_class
    @options[:padding_x]
  end

  def outer_padding_class
    @options[:outer_padding]
  end

  def content_container_class
    @options[:content_container]
  end

  def inner_container_class
    @options[:inner_container]
  end

  def mobile_container_class
    @options[:mobile_container] || container_class
  end

  def gap_y_class
    @options[:gap_y]
  end

  def alignment
    @options[:alignment] || "left"
  end

  def theme
    options[:theme]
  end

  def background_overlay_opacity
    options[:background_overlay_opacity]
  end

  def media_alignment_class
    options[:media_alignment] == "last" ? "order-last" : "order-first"
  end

  def background_color
    options[:background_color]
  end

  def media_position_class
    media.options[:position] == "right" ? "order-last" : "order-first"
  end

  def media_container_dom_id
    "media-container-#{id}"
  end

  def component
    @component ||= Rails.application.config.x.components[@name.to_sym].constantize.new(self)
  end
end
