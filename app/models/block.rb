# == Schema Information
#
# Table name: blocks
#
#  id              :bigint           not null, primary key
#  hidden_at       :datetime
#  media_options   :jsonb
#  name            :string
#  options         :jsonb
#  position        :integer
#  pricing_options :jsonb
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  media_group_id  :bigint
#  media_type_id   :bigint
#  page_id         :bigint
#  pricing_id      :bigint
#
# Indexes
#
#  index_blocks_on_media_group_id  (media_group_id)
#  index_blocks_on_media_type_id   (media_type_id)
#  index_blocks_on_pricing_id      (pricing_id)
#
# Foreign Keys
#
#  fk_rails_...  (media_group_id => media_groups.id)
#  fk_rails_...  (media_type_id => media_types.id)
#  fk_rails_...  (page_id => pages.id)
#  fk_rails_...  (pricing_id => pricing.id)
#
class Block < ApplicationRecord
  include ActionView::Helpers::SanitizeHelper
  include TailwindClassable

  # Associations
  belongs_to :page
  belongs_to :pricing, optional: true
  belongs_to :media_group, optional: true
  belongs_to :media_type, optional: true

  has_many :controls, -> { order(position: :asc) }, class_name: "BlockControl", dependent: :destroy
  has_many :media_in_group, through: :media_group, class_name: "Media", source: :media
  has_many :media, -> { order(position: :asc) }, dependent: :destroy, class_name: "Media"

  # Attachments
  has_one_attached :background_image do |attachable|
    attachable.variant :thumb, resize_to_fill: [120, 60]
    attachable.variant :preview, resize_to_limit: [ 1024, 768 ], saver: { quality: 100 }
  end

  has_one_attached :background_image_mobile

  # Scopes
  default_scope { order(position: :asc) }
  scope :visible, -> { where(hidden_at: nil) }

  # Store Accessor
  store_accessor :options, :theme, :alignment, :container, :test_container, :inner_container, :content_container, :background_overlay_opacity, :background_color, :media_alignment
  store_accessor :media_options, :inline_items_count, :posts_limit, :gap, :layout, :resize_image_options, :position, prefix: :media
  store_accessor :pricing_options, :pricing_type

  # Positioning
  positioned on: %i[page_id]

  # Nested Attributes
  accepts_nested_attributes_for :controls, allow_destroy: true

  validates :padding_class, :outer_padding_class,
            inclusion: { in: ALLOWED_PADDING_CLASSES, message: "%{value} není povolená hodnota" },
            allow_nil: false

  def hidden?
    hidden_at.present?
  end

  def last?
    page.blocks.last == self
  end

  def first?
    page.blocks.first == self
  end

  def block_name
    heading_text = controls.find { |c| c.type == "BlockControls::Heading" }&.text
    heading_text.present? ? strip_tags(heading_text) : "block-#{id}"
  end

  def to_combobox_display
    block_name
  end

  def has_media?
    media_type.present?
  end
  def has_pricing?
    name == "pricing001"
  end

  def self.initialize_from_block_object(block_object)
    require 'open-uri'

    block = new(
      name: block_object.name,
      page_id: 1,
      options: block_object.options,
      media_options: block_object.media.present? ? block_object.media.options : nil
    ).tap do |block|
      block_object.controls.each do |control|
        block.controls.build(
          type: control.type,
          options: control.options,
          text: control.respond_to?(:text) ? control.text : nil,
          position: control.position
        # content: control.content
        )
      end

      if block_object.media.present?
        required_media_count = block_object.media.items.count

        media_type = MediaType.find_by(slug: block_object.media.type)
        block.media_type = media_type

        current_media_items = Media.where(media_type:media_type).limit(required_media_count)

        if media_type.has_groups?
          media_group = MediaGroup.create(name: "Name", type: "GalleryMediaGroup", media_type: media_type)
        else
          media_group = nil
        end

        block.media_group = media_group

        if current_media_items.count < required_media_count
          block_object.media.items.each do |media_item|
            media = Media.create(
              title: media_item.title,
              content: media_item.content,
              data: media_item.data,
              media_type: media_type,
              icon: media_item.icon ? Icon.find_by_name(media_item.icon) : nil
            )

            media.media_group = media_group if media_group.present?

            if media_item.image.present?
              file = URI.open(media_item.image)
              media.image.attach(io: file, filename: File.basename(media_item.image))
            end
            media.save
          end
        end
      end
    end

    # block.save
    block
  end
end

