# == Schema Information
#
# Table name: websites
#
#  id                :bigint           not null, primary key
#  address           :string
#  available_locales :jsonb
#  city              :string
#  country           :string
#  data              :jsonb
#  domain            :string
#  email             :string
#  locale            :string           default("cs")
#  map_url           :string
#  name              :string
#  phone             :string
#  postal_code       :string
#  social_networks   :jsonb
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  account_id        :bigint           not null
# Indexes
#
#  index_websites_on_account_id  (account_id)
#
# Foreign Keys
#
#  fk_rails_...  (account_id => accounts.id)
#
class Website < ApplicationRecord
  AVAILABLE_LOCALES = %w[cs en de pl].freeze
  LOCALE_NAMES = { cs: "Češ<PERSON>", en: "English", de: "Deutsch", pl: "<PERSON>ski" }.freeze

  LOGO_SIZES = {
    "64px" => "max-w-[64px]",
    "96px" => "max-w-[96px]",
    "128px" => "max-w-[128px]",
    "160px" => "max-w-[160px]",
    "192px" => "max-w-[192px]",
    "256px" => "max-w-[256px]"
  }.freeze

  store_accessor :available_locales
  store_accessor :data, :opening_hours_text, :logo_size
  store_accessor :social_networks, :facebook, :instagram, :x, :youtube

  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :phone, presence: true, format: { with: /\A\+?[0-9\s]+\z/ }

  normalizes :email, :phone, :name, with: ->(attribute) { attribute.strip }

  belongs_to :account

  has_one_attached :logo do |attachable|
    attachable.variant :resized, resize_to_limit: [ 150, 150 ], saver: { quality: 100 }
  end

  has_many :pages, dependent: :destroy
  has_many :pricing, dependent: :destroy
  has_many :reviews, dependent: :destroy
  has_many :opening_hours, dependent: :destroy
  has_many :forms, dependent: :destroy
  has_many :inbox_messages, dependent: :destroy

  accepts_nested_attributes_for :opening_hours, allow_destroy: true
  validates_associated :opening_hours

  before_save :merge_locales

  def merge_locales
    self.available_locales = ([ locale ] + Array.wrap(available_locales)).uniq
  end

  def custom_opening_hours
    opening_hours.reject do |opening_hour|
      opening_hour.date.nil? || opening_hour.date.end_of_day < Time.now || OpeningHour.holiday_dates[Time.now.year.to_s].include?(opening_hour.date.to_date.to_s)
    end
  end

  def sms_on_reservation_confirm?
    sms_on_reservation_confirm == "1"
  end

  def sms_on_reservation_cancel?
    sms_on_reservation_cancel == "1"
  end

  def logo_size_class
    LOGO_SIZES[logo_size] || LOGO_SIZES["128px"]
  end
end
