class BlockConfigAdapter < ConfigAdapter
  def initialize(block)
    @block = block
  end

  def options
    @block.options
  end

  def controls
    @block.controls
  end

  def media
    {
      type: @block.media_type,
      layout: @block.media_layout,
      gap: @block.media_gap,
      position: @block.media_position,
      inline_items_count: @block.media_inline_items_count,
      resize_image_options: @block.media_resize_image_options&.symbolize_keys,
      media_items: @block.media + @block.media_in_group
    }
  end

  def pricing_options
    @block.pricing_options
  end
end
